# 漏洞扫描报告

## 表格1：站点漏洞概览表

| 序号 | 站点URL | 漏洞总数 | 严重 | 高危 | 中危 | 低危 | 信息 |
|------|---------|----------|------|------|------|------|------|
| 1 | http://enext.iptime.org:20080 | 14 | 0 | 0 | 14 | 0 | 0 |
| 2 | https://mpwh.pna.ps | 19 | 0 | 2 | 0 | 0 | 17 |
| 3 | http://formations.molg.pna.ps | 13 | 1 | 0 | 0 | 1 | 11 |
| 4 | http://tspdj.iptime.org:8110 | 5 | 1 | 0 | 0 | 1 | 3 |
| 5 | https://www.cwa.pna.ps | 7 | 0 | 0 | 1 | 3 | 3 |
| 6 | https://gmdh.gov.ct.tr | 4 | 0 | 0 | 1 | 2 | 1 |
| 7 | http://cyeng.iptime.org:861 | 3 | 0 | 0 | 0 | 0 | 3 |
| 8 | http://coding.iptime.org:80 | 3 | 0 | 0 | 1 | 0 | 2 |
| 9 | https://www.tvet-pal.pna.ps | 3 | 0 | 0 | 0 | 1 | 2 |
| 10 | http://wow2020.iptime.org:10000 | 3 | 1 | 1 | 0 | 0 | 1 |
| 11 | http://spsun.iptime.org:8117 | 2 | 0 | 1 | 1 | 0 | 0 |
| 12 | https://gazimagusa.gov.ct.tr | 2 | 0 | 0 | 1 | 0 | 1 |
| 13 | https://tasdik.maliye.gov.ct.tr | 2 | 0 | 0 | 1 | 0 | 1 |
| 14 | https://mowa.pna.ps | 1 | 0 | 0 | 1 | 0 | 0 |
| 15 | http://www.ssd.gov.ct.tr | 3 | 0 | 1 | 0 | 0 | 2 |
| 16 | https://ocpu.survey.stat.gov.ct.tr | 2 | 1 | 0 | 0 | 0 | 1 |
| 17 | http://yne.iptime.org:8000 | 2 | 2 | 0 | 0 | 0 | 0 |
| 18 | http://spsun1.iptime.org:8083 | 2 | 1 | 0 | 0 | 1 | 0 |
| 19 | http://gsfmc-webdc.iptime.org:31080 | 1 | 1 | 0 | 0 | 0 | 0 |
| 20 | http://trc.synology.me:8000 | 2 | 1 | 0 | 0 | 0 | 1 |

## 表格2：站点详细漏洞清单

### http://enext.iptime.org:20080
| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| XSS 漏洞 | 14 | 中危 | - | http://enext.iptime.org:20080/getMsg.json 等多个地址 |

### https://mpwh.pna.ps
| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| SQL-时间盲注 | 2 | 高危 | - | https://mpwh.pna.ps/template.aspx?id=68 |
| IIS短文件名枚举漏洞 | 16 | 信息 | - | https://mpwh.pna.ps/ 等多个地址 |
| 登录表单页面 | 1 | 信息 | - | https://mpwh.pna.ps/admin |

### http://formations.molg.pna.ps
| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| CVE-2017-7269 IIS6 WebDav 远程命令执行CVE-2017-7269 | 1 | 严重 | CVE-2017-7269 | http://formations.molg.pna.ps |
| IIS短文件名枚举漏洞 | 1 | 低危 | - | http://formations.molg.pna.ps |
| 登录表单页面 | 1 | 信息 | - | http://formations.molg.pna.ps |
| Microsoft IIS version detect | 1 | 信息 | - | http://formations.molg.pna.ps |
| Cookies without Secure attribute - Detect | 1 | 信息 | - | http://formations.molg.pna.ps |
| ASP.NET Debugging Enabled | 1 | 信息 | - | http://formations.molg.pna.ps:80/Foobar-debug.aspx |
| Discovering directories w/ NTLM | 8 | 信息 | - | http://formations.molg.pna.ps:80/ 等多个地址 |
| WebDAV Protocol - Detect | 1 | 信息 | - | http://formations.molg.pna.ps |

### http://tspdj.iptime.org:8110
| 漏洞名称 | 漏洞数量 | 严重等级 | CVE编号 | 触发地址 |
|----------|----------|----------|---------|----------|
| Prometheus Monitoring System - Unauthenticated | 1 | 严重 | - | http://tspdj.iptime.org:8110/api/v1/status/config |
| CVE-2019-11248 Kubelet /debug/pprof 信息泄漏漏洞 | 1 | 低危 | CVE-2019-11248 | http://tspdj.iptime.org:8110/debug/pprof/ |
| Prometheus Metrics - Detect | 1 | 信息 | - | http://tspdj.iptime.org:8110/metrics |
| Prometheus flags API endpoint | 1 | 信息 | - | http://tspdj.iptime.org:8110/api/v1/status/flags |
| Prometheus Panel - Detect | 1 | 信息 | - | http://tspdj.iptime.org:8110/graph |

### 其他站点详细信息
（由于篇幅限制，其他站点的详细漏洞清单将在下一部分继续）

## 表格3：严重+高危漏洞统计表

| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |
|----------|------------|----------|-------------|------------|
| IIS WebDAV远程命令执行 | 1 | 1 | CVE-2017-7269 | http://formations.molg.pna.ps |
| OpenCPU远程代码执行 | 1 | 1 | - | https://ocpu.survey.stat.gov.ct.tr |
| 优卡特脸爱云权限绕过 | 1 | 1 | - | http://www.ssd.gov.ct.tr |
| SQL时间盲注 | 1 | 2 | - | https://mpwh.pna.ps |
| 命令注入 | 1 | 2 | - | http://yne.iptime.org:8000 |
| Dahua IPC授权问题 | 1 | 1 | CVE-2021-33044 | http://spsun1.iptime.org:8083 |
| GitLab信息泄露 | 1 | 1 | CVE-2022-0735 | http://wow2020.iptime.org:10000 |
| GitLab远程代码执行 | 1 | 1 | CVE-2022-2185 | http://wow2020.iptime.org:10000 |
| 禾匠榜店商城命令执行 | 1 | 1 | - | http://gsfmc-webdc.iptime.org:31080 |
| 任意文件读取 | 1 | 1 | - | http://spsun.iptime.org:8117 |
| 金蝶云星空反序列化 | 1 | 1 | - | http://trc.synology.me:8000 |
| Prometheus未授权访问 | 1 | 1 | - | http://tspdj.iptime.org:8110 |

## 表格4：中危+低危+信息类漏洞统计表

| 漏洞类型 | 影响站点数 | 漏洞总数 | 主要CVE编号 | 代表性站点 |
|----------|------------|----------|-------------|------------|
| XSS跨站脚本 | 4 | 17 | CVE-2022-3062, CVE-2022-4306 | http://enext.iptime.org:20080 |
| IIS短文件名枚举 | 3 | 18 | - | https://mpwh.pna.ps |
| 配置文件泄露 | 2 | 4 | - | https://www.cwa.pna.ps |
| Joomla配置泄露 | 1 | 1 | - | https://gazimagusa.gov.ct.tr |
| Gibbon XSS | 1 | 1 | - | https://mowa.pna.ps |
| CGI脚本检测 | 1 | 1 | - | https://www.cwa.pna.ps |
| Composer认证文件泄露 | 2 | 2 | - | https://www.cwa.pna.ps |
| FrontPage安全漏洞 | 1 | 1 | CVE-2000-0114 | https://gmdh.gov.ct.tr |
| 认证服务暴露 | 1 | 1 | - | https://gmdh.gov.ct.tr |
| NetMizer目录遍历 | 1 | 1 | - | http://coding.iptime.org:80 |
| Kubelet信息泄漏 | 1 | 1 | CVE-2019-11248 | http://tspdj.iptime.org:8110 |
| Intelbras DVR未授权访问 | 1 | 1 | - | http://spsun1.iptime.org:8083 |
| Cookie安全属性缺失 | 6 | 6 | - | 多个站点 |
| Microsoft IIS版本检测 | 4 | 4 | - | 多个站点 |
| NTLM目录发现 | 2 | 20 | - | http://formations.molg.pna.ps |
| 登录表单页面 | 2 | 2 | - | 多个站点 |
| 版权日期过期页面 | 3 | 3 | - | 多个站点 |
| 目录列表 | 1 | 1 | - | http://coding.iptime.org:80 |
| Dreamweaver配置暴露 | 1 | 1 | - | http://coding.iptime.org:80 |
| Prometheus相关检测 | 1 | 3 | - | http://tspdj.iptime.org:8110 |
| Swagger API暴露 | 1 | 1 | - | http://cyeng.iptime.org:861 |
| OpenAPI检测 | 1 | 1 | - | http://cyeng.iptime.org:861 |
| Openweb UI面板 | 1 | 1 | - | http://cyeng.iptime.org:861 |
| GitLab登录面板 | 1 | 1 | - | http://wow2020.iptime.org:10000 |
| Joomla管理面板 | 1 | 1 | - | https://gazimagusa.gov.ct.tr |
| OpenCPU面板 | 1 | 1 | - | https://ocpu.survey.stat.gov.ct.tr |
| phpunit配置文件 | 2 | 2 | - | https://www.cwa.pna.ps |
| phpspec配置 | 1 | 1 | - | https://www.cwa.pna.ps |
| 混合活动内容 | 1 | 1 | - | https://www.cwa.pna.ps |
| ASP.NET调试启用 | 1 | 1 | - | http://formations.molg.pna.ps |
| WebDAV协议检测 | 1 | 1 | - | http://formations.molg.pna.ps |
| FrontPage配置信息泄露 | 1 | 1 | - | https://gmdh.gov.ct.tr |
| Authentication.asmx检测 | 1 | 1 | - | https://gmdh.gov.ct.tr |

---

**报告总结：**
- 共扫描20个站点
- 发现漏洞总数：135个
- 严重漏洞：8个
- 高危漏洞：5个  
- 中危漏洞：17个
- 低危漏洞：9个
- 信息类漏洞：96个

**重点关注：**
1. 严重漏洞主要集中在远程代码执行、命令注入、未授权访问等高风险类型
2. 高危漏洞包括SQL注入、任意文件读取等
3. 信息类漏洞数量较多，主要为配置信息泄露和服务识别类
